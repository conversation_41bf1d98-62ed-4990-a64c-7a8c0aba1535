// Test script for the Mastra-based CodeOCR API
const testMastraAPI = async () => {
  try {
    const response = await fetch('http://localhost:3000/api/v1/codeocr', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image_source: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 transparent PNG
        model_name: 'gpt-4o-mini',
        api_key: process.env.OPENAI_API_KEY || 'test-key',
        api_base: 'https://api.openai.com/v1'
      })
    });

    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test if this file is executed directly
if (require.main === module) {
  testMastraAPI();
}

module.exports = { testMastraAPI };
