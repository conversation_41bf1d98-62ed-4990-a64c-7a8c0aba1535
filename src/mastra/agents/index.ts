import { Agent } from "@mastra/core/agent";
import { createOpenAI } from "@ai-sdk/openai";
import { CODE_OCR_PROMPT } from "@/lib/prompts";

// Create a default CodeOCR agent with OpenAI
export const codeOcrAgent = new Agent({
  name: "CodeOCR Agent",
  instructions: CODE_OCR_PROMPT,
  model: createOpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  })("gpt-4o-mini"),
});

// Factory function to create a CodeOCR agent with custom configuration
export const createCodeOcrAgent = (modelConfig: {
  model_name: string;
  api_key?: string;
  api_base?: string;
}) => {
  const openai = createOpenAI({
    apiKey: modelConfig.api_key || process.env.OPENAI_API_KEY,
    baseURL: modelConfig.api_base,
  });
  
  return new Agent({
    name: "CodeOCR Agent",
    instructions: CODE_OCR_PROMPT,
    model: openai(modelConfig.model_name),
  });
};
