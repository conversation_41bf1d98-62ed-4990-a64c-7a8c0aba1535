import {
  Facebook,
  Github,
  Instagram,
  Jue<PERSON>in,
  Linkedin,
  Mail,
  Mastodon,
  Threads,
  Twitter,
  TwitterX,
  WeChat,
  Youtube,
} from "./icons";

const components = {
  mail: Mail,
  github: Github,
  facebook: Facebook,
  youtube: Youtube,
  linkedin: <PERSON>ed<PERSON>,
  twitter: Twitter,
  twitterX: TwitterX,
  weChat: WeChat,
  jueJin: <PERSON><PERSON><PERSON><PERSON>,
  mastodon: <PERSON><PERSON><PERSON>,
  threads: Threads,
  instagram: Instagram,
};

type SocialIconProps = {
  kind: keyof typeof components;
  href: string | undefined;
  size?: number;
};

const SocialIcon = ({ kind, href, size = 8 }: SocialIconProps) => {
  if (
    !href ||
    (kind === "mail" &&
      !/^mailto:\w+([.-]?\w+)@\w+([.-]?\w+)(.\w{2,3})+$/.test(href))
  )
    return null;

  const SocialSvg = components[kind];

  return (
    <a
      className="text-sm text-gray-500 transition hover:text-gray-600"
      target="_blank"
      rel="noopener noreferrer"
      href={href}
    >
      <span className="sr-only">{kind}</span>
      <SocialSvg
        className={`fill-current text-gray-400 hover:text-primary-500 dark:text-gray-200 dark:hover:text-primary-400 h-${size} w-${size}`}
      />
    </a>
  );
};

export default SocialIcon;
