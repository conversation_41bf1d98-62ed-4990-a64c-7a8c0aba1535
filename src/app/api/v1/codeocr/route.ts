import { NextResponse } from "next/server";
import { CodeOcrRequestSchema, CodeOcrResponseSchema } from "@/lib/schemas";
import { createCodeOcrAgent } from "@/mastra/agents";

export const runtime = "edge";

export async function POST(req: Request) {
  try {
    // 1. Validate request body
    const body = await req.json();
    const validation = CodeOcrRequestSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid request", details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { image_source, model_name, api_key, api_base } = validation.data;

    // 2. Create Mastra Agent for Code OCR with dynamic configuration
    const codeOcrAgent = createCodeOcrAgent({
      model_name,
      api_key,
      api_base,
    });

    // 3. Use Mastra Agent to generate structured output
    const response = await codeOcrAgent.generate(
      [
        {
          role: "user",
          content: [
            { type: "text", text: "Please extract the code from this image:" },
            { type: "image", image: image_source },
          ],
        },
      ],
      {
        output: CodeOcrResponseSchema,
      }
    );

    // 4. Return the structured, validated response
    return NextResponse.json(response.object);
  } catch (error: any) {
    console.error("Error in CodeOCR API route:", error);
    return NextResponse.json(
      {
        error: "An unexpected error occurred.",
        details: error.message || "Unknown error",
      },
      { status: 500 }
    );
  }
}
