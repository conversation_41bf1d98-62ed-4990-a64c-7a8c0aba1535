import { createOpenAI } from "@ai-sdk/openai";
import { generateText } from "ai";

const openai = createOpenAI({
  apiKey: "acba477d1d394b088289527f2dc5f908.1bIzK4Rcg7edpmwV",
  baseURL: "https://open.bigmodel.cn/api/paas/v4/",
});

const req = {
  json: async () => ({
    prompt: "你是什么模型",
  }),
};

async function POST(req: any) {
  const { prompt } = await req.json();

  const { text } = await generateText({
    model: openai("glm-4v-flash"),
    system: "You are a helpful assistant.",
    prompt,
  });

  return text;
}

const result = await POST(req);
console.log(result);


import { Mastra } from "@mastra/core";
import { Agent } from "@mastra/core/agent";
import { z } from "zod";

const myAgent = new Agent({
  name: "My Agent",
  instructions: "You are a helpful assistant.",
  model: openai("glm-4v-flash"),
});

const mastra = new Mastra({
  agents: { myAgent },
});


const response = await myAgent.generate([{ role: "user", content: "你好啊" }]);

console.log("Agent:", response.text);
